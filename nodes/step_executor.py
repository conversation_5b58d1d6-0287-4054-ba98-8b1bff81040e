#!/usr/bin/env python3
"""
步骤执行节点

负责执行当前测试步骤
"""

import base64
from datetime import datetime

from openai import OpenAI

from tools.screenshot_utils import take_screenshot

from data.State import DeploymentState


def execute_current_step_node(state: DeploymentState) -> DeploymentState:
    """
    Execute the complete test case - let model see all steps and decide execution flow
    """
    # Check if already completed
    if state.get("completed", False):
        print("✓ Test case already completed")
        return state

    # Save screenshot before execution
    before_screenshot = state["current_page"].get("screenshot", "")

    try:
        # Execute using enhanced get_location with full context
        state = enhanced_get_location(state)

        # Check if execution was successful
        execution_success = True
        if len(state["history"]) > 0:
            last_record = state["history"][-1]
            if last_record.get("status") == "error":
                execution_success = False

        # Add execution record
        execution_record = {
            "action": "test_case_execution",
            "before_screenshot": before_screenshot,
            "execution_success": execution_success,
            "timestamp": datetime.now().isoformat(),
            "total_steps": len(state.get("task_steps", [])),
            "completed_steps": state.get("completed_steps", 0)
        }

        state["history"].append(execution_record)

    except Exception as e:
        print(f"❌ Error executing test case: {str(e)}")
        # Add error record
        error_record = {
            "action": "test_case_execution",
            "error": str(e),
            "status": "error",
            "timestamp": datetime.now().isoformat()
        }
        state["history"].append(error_record)

        state["step_failed"] = True
        state["retry_count"] += 1

    return state


def build_complete_test_case_messages(state: DeploymentState) -> list:
    """
    构建包含完整测试用例上下文的消息列表
    """
    messages = []

    # 获取测试用例信息
    test_case_name = state.get("test_case_name", "未知测试用例")
    test_case_description = state.get("test_case_description", state.get("task", ""))
    expected_result = state.get("expected_result", "")

    # 构建完整的测试用例上下文
    system_instruction = f'''
## 角色
你是一个安卓测试用例自动化助手。你需要执行完整的测试用例，而不是单个步骤。
你能够看到完整的测试用例步骤列表，并且可以根据当前界面状态自主决定执行策略。

## 测试用例信息
**测试用例名称**: {test_case_name}
**测试描述**: {test_case_description}
**期望结果**: {expected_result}

'''
    system_instruction += f'''

## 输出格式
```
Thought: ...
Action: ...
```

## 动作列表
click(start_box='<|box_start|>(x1, y1)<|box_end|>') # 点击屏幕
long_press(start_box='<|box_start|>(x1, y1)<|box_end|>') # 长按屏幕
drag(start_box='<|box_start|>(x1, y1)<|box_end|>', end_box='<|box_start|>(x3, y3)<|box_end|>') # 滑动屏幕
type(content='') #If you want to submit your input, use "\\n" at the end of `content`.
back # 返回上一页,
wait() # Sleep for 5s and take a screenshot to check for any changes.
finished(content='xxx') # 当整个测试用例完成时调用，content应该包含测试结果总结

## 执行指导
- 你可以看到完整的测试用例步骤，请根据当前界面状态决定下一步操作
- 不需要严格按照步骤顺序执行，可以根据界面状态灵活调整
- 当你认为整个测试用例已经完成（达到期望结果）时，调用finished()
- 在Thought中分析当前界面状态，判断应该执行哪个步骤或操作
- 如果遇到无法确定的界面状态，及时使用back退回到上一步

## Note
- `Thought`使用中文进行思考
- 分析当前界面状态，判断测试用例的执行进度
- 根据完整的步骤列表和期望结果，决定下一步最合适的操作
- 只有当整个测试用例完成时才调用finished()
'''

    messages.append({
        "role": "user",
        "content": system_instruction
    })

    # 添加执行历史 - 包含所有相关的执行记录
    history = state.get("history", [])
    execution_records = [r for r in history if r.get("action") == "enhanced_get_location" and r.get("model_response")]

    # 添加最近的执行历史（保留更多上下文）
    recent_records = execution_records[-10:] if len(execution_records) > 10 else execution_records
    for record in recent_records:
        if record.get("model_response"):
            messages.append({
                "role": "assistant",
                "content": record["model_response"]
            })

    return messages


def enhanced_get_location(state: DeploymentState) -> DeploymentState:
    """
    Enhanced version of get_location with comprehensive context
    """
    from tools.action_agent import execute_simple_action
    from openai import OpenAI

    # Initialize model
    client = OpenAI(
        base_url="http://10.65.230.19:8005/v1",
        api_key="aaa"
    )

    # 每次循环都获取最新的截图
    screenshot_path = take_screenshot(
        device=state["device"],
        app_name="deployment",
        step=state.get("current_step", 0)
    )

    # 更新当前页面截图
    state["current_page"]["screenshot"] = screenshot_path

    try:
        with open(screenshot_path, "rb") as f:
            image_content = f.read()
        image_data_base64 = base64.b64encode(image_content).decode("utf-8")
    except Exception as e:
        print(f"❌ Failed to read screenshot: {str(e)}")
        state["history"].append({
            "action": "enhanced_get_location",
            "error": f"Failed to read screenshot: {str(e)}",
            "status": "error"
        })
        return state

    # Build messages with complete test case context
    messages = build_complete_test_case_messages(state)

    # Add current screenshot
    messages.append({
        "role": "user",
        "content": [
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{image_data_base64}"
                }
            }
        ]
    })

    try:
        # Call model

        chat_completion = client.chat.completions.create(
            model="ByteDance-Seed/UI-TARS-1.5-7B",
            messages=messages,
            top_p=None,
            temperature=0.1,
            max_tokens=400,
            stream=True,
            seed=None,
            stop=None,
            frequency_penalty=None,
            presence_penalty=None
        )

        response = ""
        for message in chat_completion:
            if message.choices[0].delta.content:
                response += message.choices[0].delta.content

        model_response = response
        print(f"Model response: {model_response}")
        # Extract action from response
        action_line = ""
        for line in model_response.split('\n'):
            if line.strip().startswith('Action:'):
                action_line = line.replace('Action:', '').strip()
                break

        if action_line:
            # Check if this is a finished action
            if action_line.startswith("finished("):
                print(f"✅ Test case completed: {action_line}")

                # 标记整个测试用例完成
                state["completed"] = True
                state["execution_status"] = "completed"
                state["step_failed"] = False
                state["retry_count"] = 0

                # Add completion record
                state["history"].append({
                    "action": "test_case_completed",
                    "model_response": model_response,
                    "completion_action": action_line,
                    "total_steps": len(state.get("task_steps", [])),
                    "completed_steps": state.get("completed_steps", 0),
                    "status": "completed",
                    "timestamp": datetime.now().isoformat()
                })

                print(f"🎉 Test case execution completed successfully!")
                return state

            # Execute action using simple action executor
            action_result = execute_simple_action(action_line, state["device"])

            print(f"✓ Action executed: {action_line}")
            print(f"⏳ Continuing test case execution until finished() is called...")

            # Add to history
            state["history"].append({
                "action": "enhanced_get_location",
                "model_response": model_response,
                "parsed_action": action_line,
                "action_result": action_result,
                "total_steps": len(state.get("task_steps", [])),
                "completed_steps": state.get("completed_steps", 0),
                "status": "success" if action_result.get("status") == "success" else "error",
                "timestamp": datetime.now().isoformat()
            })
        else:
            print(f"❌ Failed to extract action from: {model_response}")
            state["history"].append({
                "action": "enhanced_get_location",
                "model_response": model_response,
                "error": "Failed to extract action",
                "status": "error",
                "timestamp": datetime.now().isoformat()
            })

    except Exception as e:
        print(f"❌ Error in enhanced_get_location: {str(e)}")
        state["history"].append({
            "action": "enhanced_get_location",
            "error": str(e),
            "status": "error",
            "timestamp": datetime.now().isoformat()
        })

    return state